{"root": "packages/shared", "sourceRoot": "packages/shared/src", "projectType": "library", "targets": {"build": {"executor": "@nrwl/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/shared", "main": "packages/shared/src/index.ts", "tsConfig": "packages/shared/tsconfig.lib.json", "assets": ["packages/shared/*.md"]}}, "lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["packages/shared/**/*.ts"]}}}, "tags": []}