{"root": "packages/plugin", "sourceRoot": "packages/plugin/src", "projectType": "application", "targets": {"build": {"executor": "@nrwl/node:build", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/plugin", "main": "packages/plugin/src/main.ts", "tsConfig": "packages/plugin/tsconfig.app.json", "assets": ["packages/plugin/src/assets", {"glob": "manifest.json", "input": "./packages/plugin/src/app", "output": "."}, {"glob": "options.html", "input": "./packages/plugin/src/app", "output": "."}, {"glob": "firebaseui.css", "input": "./node_modules/firebaseui/dist", "output": "."}], "externalDependencies": "none", "webpackConfig": "packages/plugin/webpack.config.js"}, "configurations": {"production": {"optimization": true, "extractLicenses": true, "inspect": false, "fileReplacements": [{"replace": "packages/plugin/src/environments/environment.ts", "with": "packages/plugin/src/environments/environment.prod.ts"}]}}}, "serve": {"executor": "@nrwl/node:execute", "options": {"buildTarget": "plugin:build"}}, "lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["packages/plugin/**/*.ts"]}}}, "tags": []}