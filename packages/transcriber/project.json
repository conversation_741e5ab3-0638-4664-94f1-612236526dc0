{"root": "packages/transcriber", "sourceRoot": "packages/transcriber/src", "projectType": "library", "targets": {"build": {"executor": "@nrwl/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/transcriber", "main": "packages/transcriber/src/index.ts", "tsConfig": "packages/transcriber/tsconfig.lib.json", "assets": ["packages/transcriber/*.md"]}}, "lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["packages/transcriber/**/*.ts"]}}}, "tags": []}