{"root": "packages/chrome-extension", "sourceRoot": "packages/chrome-extension/src", "projectType": "application", "targets": {"build": {"executor": "@nrwl/node:build", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/chrome-extension", "main": "packages/chrome-extension/src/app/popup.ts", "tsConfig": "packages/chrome-extension/tsconfig.app.json", "assets": ["packages/chrome-extension/src/assets", {"glob": "manifest.json", "input": "./packages/chrome-extension/src/app", "output": "."}, {"glob": "popup.html", "input": "./packages/chrome-extension/src/app", "output": "."}, {"glob": "firebaseui.css", "input": "./node_modules/firebaseui/dist", "output": "."}], "externalDependencies": "none", "webpackConfig": "packages/chrome-extension/webpack.config.js"}, "configurations": {"production": {"optimization": true, "extractLicenses": true, "inspect": false, "fileReplacements": [{"replace": "packages/chrome-extension/src/environments/environment.ts", "with": "packages/chrome-extension/src/environments/environment.prod.ts"}]}, "development": {"optimization": false, "extractLicenses": false}}, "defaultConfiguration": "production"}, "serve": {"executor": "@nrwl/node:execute", "options": {"buildTarget": "chrome-extension:build:development"}}, "lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["packages/chrome-extension/**/*.ts"]}}}, "tags": []}