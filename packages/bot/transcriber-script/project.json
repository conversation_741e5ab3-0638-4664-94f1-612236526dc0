{"root": "packages/bot/transcriber-script", "sourceRoot": "packages/bot/transcriber-script/src", "projectType": "library", "targets": {"build": {"executor": "@nrwl/node:build", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/bot/transcriber-script", "main": "packages/bot/transcriber-script/src/index.ts", "tsConfig": "packages/bot/transcriber-script/tsconfig.lib.json", "externalDependencies": "none", "deleteOutputPath": false, "webpackConfig": "packages/bot/transcriber-script/webpack.config.js"}}, "lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["packages/bot/transcriber-script/**/*.ts"]}}}, "tags": []}