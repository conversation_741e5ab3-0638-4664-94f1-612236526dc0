import { InjectionToken, Provider } from 'injection-js';
import os from 'os';
import { authenticator } from 'otplib';
import { <PERSON><PERSON><PERSON> } from 'puppeteer';
import puppeteer from 'puppeteer-extra';
import AnonymizeUaPlugin from 'puppeteer-extra-plugin-anonymize-ua';
import StealthPlugin from 'puppeteer-extra-plugin-stealth';

import { Logger } from '@kurt/shared';

import { environment } from '../environments/environment';
const stealth = StealthPlugin()
stealth.enabledEvasions.delete('iframe.contentWindow')
stealth.enabledEvasions.delete('media.codecs')

puppeteer.use(stealth).use(AnonymizeUaPlugin());

export const BROWSER = new InjectionToken<Browser>('Browser');

// Utility function to replace deprecated waitForTimeout
const delay = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

export const provideBrowser = async (logger: Logger): Promise<Provider> => {
  logger.log('Launching browser.');

  let executablePath: string;
  if (os.platform() === 'win32') {
    executablePath = 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe';
  } else {
    executablePath = 'google-chrome-stable';
  }
  const browser = await puppeteer.launch({
    headless: false,
    executablePath: executablePath,
    dumpio: environment.production,

    args: [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--use-fake-ui-for-media-stream',
      '--disable-gpu',
      '--window-size=1920,1080',
      '--start-maximized',
      '--mute-audio',
      '--incognito',
      '--disable-dev-shm-usage',
      '--disable-setuid-sandbox',
      '--enable-logging=stderr',
      '--v=1 > /tmp/log-chrome.log 2>&1',
      '--crash-dumps-dir=/tmp',
      '--enable-crash-reporter',
      '--disable-software-rasterizer',
    ],
  });

  try {
    const context = browser.defaultBrowserContext();

    const page = await context.newPage();

    await context.overridePermissions(
      'https://meet.google.com.rproxy.goskope.com',
      ['camera', 'microphone', 'notifications']
    );

    logger.log('Navigating to Google login.');
    // await page.setBypassCSP(true);
    await page.goto('https://accounts.google.com/signin/v2/identifier', {
      waitUntil: 'networkidle2',
    });

    logger.log('Waiting for username field.');
    await page.waitForSelector('#identifierId');
    await delay(1000);
    await page.type('#identifierId', '<EMAIL>', { delay: 100 });
    await delay(1000);
    await page.keyboard.press('Enter');

    logger.log('Waiting for onelogin page.');
    await page.waitForNavigation({
      waitUntil: 'networkidle2',
    });
    await delay(1000);

    //wait for the cookies button to accept them
    await page.waitForSelector('button[id="onetrust-accept-btn-handler"]');
    await delay(1000);
    await page.click('button[id="onetrust-accept-btn-handler"]');
    await delay(1000);
    //wait for the username field to appear
    await page.waitForSelector('input[id="username"]');
    await delay(1000);
    await page.type('input[id="username"]', '<EMAIL>', {
      delay: 100,
    });
    await delay(1000);
    await page.keyboard.press('Enter');

    logger.log('Waiting for password field.');
    await delay(1000);
    await page.waitForSelector('input[type="password"]');
    await delay(1000);
    await page.type('input[type="password"]', '.Vh3hcA5a;T6axX*uFok', {
      delay: 500,
    });
    await delay(1000);
    await page.keyboard.press('Enter');

    logger.log('Waiting for OTP field.');
    await delay(1000);
    await page.waitForSelector('#security-code');
    await delay(1000);
    const token = authenticator.generate('BKQXLA5DQQQPI2ROKSSX2VVV3X3L6RYB');
    await page.type('#security-code', token, { delay: 100 });
    await delay(1000);
    await page.keyboard.press('Enter');

    logger.log('Waiting for login success navigation.');
    await page.waitForNavigation({
      waitUntil: 'networkidle2',
    });
    await delay(25000);
    await page.close();

    logger.log('Logged in.');

    if (environment.production) {
      setInterval(async () => {
        const keepAlivePage = await context.newPage();

        await keepAlivePage.goto('https://meet.google.com.rproxy.goskope.com', {
          waitUntil: 'networkidle2',
        });

        await keepAlivePage.close();
      }, 60 * 1000);
    }

    browser.on('disconnected', async () => {
      logger.error(new Error('Browser disconnected'));
      try {
        await browser.close();
      } catch {
        // nothing to do
      }
    });
  } catch (e) {
    logger.error(new Error('Login failed.'));
    logger.error(e as Error);
  }

  return {
    provide: BROWSER,
    useValue: browser,
  };
};
