import { KeyManagementServiceClient } from '@google-cloud/kms';
import crc32c from 'fast-crc32c';
import { Injectable } from 'injection-js';

import serviceAccount from '../../d-nbi-kurt-b1199778f374.json';

@Injectable()
export class Encrypter {
  private readonly client = new KeyManagementServiceClient({
    credentials: serviceAccount,
  });
  private readonly keyName = this.client.cryptoKeyPath(
    'd-nbi-kurt',
    'europe-west3',
    'kurt-key-ring',
    'kurt-key'
  );

  async encrypt(text: string) {
    const plaintextCrc32c = crc32c.calculate(text);
    const [encryptResponse] = await this.client.encrypt({
      name: this.keyName,
      plaintext: Buffer.from(text),
      plaintextCrc32c: {
        value: plaintextCrc32c,
      },
    });
    const version = this.client.matchCryptoKeyVersionFromCryptoKeyVersionName(
      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      encryptResponse.name!
    );

    const ciphertext = (encryptResponse.ciphertext as Buffer).toString(
      'base64'
    );

    // Optional, but recommended: perform integrity verification on encryptResponse.
    // For more details on ensuring E2E in-transit integrity to and from Cloud KMS visit:
    // https://cloud.google.com/kms/docs/data-integrity-guidelines
    if (!encryptResponse.verifiedPlaintextCrc32c) {
      throw new Error('Encrypt: request corrupted in-transit');
    }
    if (
      crc32c.calculate(encryptResponse.ciphertext as Buffer) !==
      Number(encryptResponse.ciphertextCrc32c?.value)
    ) {
      throw new Error('Encrypt: response corrupted in-transit');
    }

    return { text: ciphertext, version };
  }
}
