{"name": "kurt", "version": "0.0.0", "license": "MIT", "scripts": {"gcp-build": "nx run bot:build-script && nx build bot", "deploy": "gcloud app deploy"}, "private": true, "devDependencies": {"@nrwl/cli": "13.7.1", "@nrwl/eslint-plugin-nx": "13.7.1", "@nrwl/express": "^13.7.1", "@nrwl/js": "13.7.1", "@nrwl/linter": "13.7.1", "@nrwl/node": "13.7.1", "@nrwl/tao": "13.7.1", "@nrwl/workspace": "13.7.1", "@types/chrome": "^0.0.177", "@types/cors": "^2.8.12", "@types/express": "4.17.13", "@types/fast-crc32c": "^2.0.0", "@types/fluent-ffmpeg": "^2.1.20", "@types/node": "16.11.7", "@typescript-eslint/eslint-plugin": "~5.10.0", "@typescript-eslint/parser": "~5.10.0", "eslint": "~8.7.0", "eslint-config-prettier": "8.1.0", "eslint-plugin-import": "^2.26.0", "prettier": "^2.5.1", "typescript": "~4.5.2"}, "dependencies": {"@google-cloud/kms": "^3.0.1", "body-parser": "^1.20.0", "cors": "^2.8.5", "date-fns": "^2.28.0", "express": "4.17.2", "fast-crc32c": "^2.0.0", "ffmpeg-stream": "^0.7.0", "firebase": "^9.6.6", "firebase-admin": "^10.0.2", "firebaseui": "^6.0.0", "injection-js": "^2.4.0", "otplib": "^12.0.1", "puppeteer": "^15.3.0", "puppeteer-extra": "^3.3.0", "puppeteer-extra-plugin-anonymize-ua": "^2.4.0", "puppeteer-extra-plugin-stealth": "^2.10.1", "reflect-metadata": "^0.1.13", "rm": "^0.1.8", "tslib": "^2.0.0", "xvfb": "^0.4.0"}}