{"name": "kurt", "version": "0.0.0", "license": "MIT", "scripts": {"gcp-build": "nx run bot:build-script && nx build bot", "deploy": "gcloud app deploy"}, "private": true, "devDependencies": {"@nx/eslint-plugin": "17.3.2", "@nx/express": "17.3.2", "@nx/js": "17.3.2", "@nx/linter": "17.3.2", "@nx/node": "17.3.2", "@nx/workspace": "17.3.2", "@types/chrome": "^0.0.254", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/fluent-ffmpeg": "^2.1.24", "@types/node": "^18.19.31", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "nx": "17.3.2", "prettier": "^3.2.5", "typescript": "^5.3.3"}, "dependencies": {"@google-cloud/kms": "^4.2.1", "body-parser": "^1.20.2", "cors": "^2.8.5", "crc-32": "^1.2.2", "date-fns": "^2.30.0", "express": "^4.19.2", "ffmpeg-stream": "^0.7.0", "firebase": "^10.11.1", "firebase-admin": "^12.1.0", "firebaseui": "^6.1.0", "injection-js": "^2.4.0", "otplib": "^12.0.1", "puppeteer": "^21.11.0", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-anonymize-ua": "^2.4.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "reflect-metadata": "^0.1.14", "rm": "^0.1.8", "tslib": "^2.6.2", "xvfb": "^0.4.0"}}