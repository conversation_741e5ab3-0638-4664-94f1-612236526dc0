[NX Daemon Server] - 2025-05-24T17:03:04.902Z - Started listening on: /var/folders/qx/bdfv_k393430l_z3_sv58f7c0000gn/T/67119523c45dc649fc57/d.sock
[NX Daemon Server] - 2025-05-24T17:03:04.903Z - [WATCHER]: Subscribed to changes within: /Users/<USER>/Documents/kurt/google-meet-chrome-plugin (native)
[NX Daemon Server] - 2025-05-24T17:03:04.910Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-05-24T17:03:04.911Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-05-24T17:03:04.921Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-05-24T17:03:04.921Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-05-24T17:03:04.921Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-05-24T17:03:04.922Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-05-24T17:03:04.939Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-05-24T17:03:04.939Z - [REQUEST]: 
[NX Daemon Server] - 2025-05-24T17:03:04.939Z - [REQUEST]: 
[NX Daemon Server] - 2025-05-24T17:03:04.979Z - Error detected when creating a project graph: Configuration Error
The following implicitDependencies point to non-existent project(s):
  bot
    bot-transcriber-script
[NX Daemon Server] - 2025-05-24T17:03:04.979Z - [REQUEST]: Responding to the client with an error. Error when preparing serialized project graph. Configuration Error
The following implicitDependencies point to non-existent project(s):
  bot
    bot-transcriber-script
Error: Configuration Error
The following implicitDependencies point to non-existent project(s):
  bot
    bot-transcriber-script
    at assertWorkspaceValidity (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/utils/assert-workspace-validity.js:70:11)
    at buildProjectGraphUsingProjectFileMap (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/project-graph/build-project-graph.js:48:61)
    at createAndSerializeProjectGraph (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/project-graph-incremental-recomputation.js:175:124)
    at processFilesAndCreateAndSerializeProjectGraph (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/project-graph-incremental-recomputation.js:142:16)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async getCachedSerializedProjectGraphPromise (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/project-graph-incremental-recomputation.js:43:16)
    at async handleRequestProjectGraph (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/handle-request-project-graph.js:12:24)
    at async handleResult (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/server.js:110:16)
    at async handleMessage (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/server.js:81:9)
    at async /Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/server.js:45:9
[NX Daemon Server] - 2025-05-24T17:03:04.979Z - Done responding to the client null
[NX Daemon Server] - 2025-05-24T17:03:05.140Z - Started listening on: /var/folders/qx/bdfv_k393430l_z3_sv58f7c0000gn/T/67119523c45dc649fc57/d.sock
[NX Daemon Server] - 2025-05-24T17:03:05.141Z - [WATCHER]: Subscribed to changes within: /Users/<USER>/Documents/kurt/google-meet-chrome-plugin (native)
[NX Daemon Server] - 2025-05-24T17:03:05.145Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-05-24T17:03:05.146Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-05-24T17:03:05.146Z - Closed a connection. Number of open connections: 1
[NX Daemon Server] - 2025-05-24T17:03:05.147Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-05-24T17:03:05.147Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-05-24T17:03:05.147Z - [REQUEST]: 
[NX Daemon Server] - 2025-05-24T17:03:05.147Z - [REQUEST]: 
[NX Daemon Server] - 2025-05-24T17:03:05.168Z - Error detected when creating a project graph: Configuration Error
The following implicitDependencies point to non-existent project(s):
  bot
    bot-transcriber-script
[NX Daemon Server] - 2025-05-24T17:03:05.168Z - [REQUEST]: Responding to the client with an error. Error when preparing serialized project graph. Configuration Error
The following implicitDependencies point to non-existent project(s):
  bot
    bot-transcriber-script
Error: Configuration Error
The following implicitDependencies point to non-existent project(s):
  bot
    bot-transcriber-script
    at assertWorkspaceValidity (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/utils/assert-workspace-validity.js:70:11)
    at buildProjectGraphUsingProjectFileMap (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/project-graph/build-project-graph.js:48:61)
    at createAndSerializeProjectGraph (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/project-graph-incremental-recomputation.js:175:124)
    at processFilesAndCreateAndSerializeProjectGraph (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/project-graph-incremental-recomputation.js:142:16)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async getCachedSerializedProjectGraphPromise (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/project-graph-incremental-recomputation.js:43:16)
    at async handleRequestProjectGraph (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/handle-request-project-graph.js:12:24)
    at async handleResult (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/server.js:110:16)
    at async handleMessage (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/server.js:81:9)
    at async /Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/server.js:45:9
[NX Daemon Server] - 2025-05-24T17:03:05.169Z - Done responding to the client null
[NX Daemon Server] - 2025-05-24T17:03:06.536Z - Started listening on: /var/folders/qx/bdfv_k393430l_z3_sv58f7c0000gn/T/67119523c45dc649fc57/d.sock
[NX Daemon Server] - 2025-05-24T17:03:06.539Z - [WATCHER]: Subscribed to changes within: /Users/<USER>/Documents/kurt/google-meet-chrome-plugin (native)
[NX Daemon Server] - 2025-05-24T17:03:06.548Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-05-24T17:03:06.549Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-05-24T17:03:06.555Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-05-24T17:03:06.555Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-05-24T17:03:06.555Z - Closed a connection. Number of open connections: 1
[NX Daemon Server] - 2025-05-24T17:03:06.556Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-05-24T17:03:06.556Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-05-24T17:03:06.557Z - [REQUEST]: 
[NX Daemon Server] - 2025-05-24T17:03:06.557Z - [REQUEST]: 
[NX Daemon Server] - 2025-05-24T17:03:06.588Z - Error detected when creating a project graph: Configuration Error
The following implicitDependencies point to non-existent project(s):
  bot
    bot-transcriber-script
[NX Daemon Server] - 2025-05-24T17:03:06.588Z - [REQUEST]: Responding to the client with an error. Error when preparing serialized project graph. Configuration Error
The following implicitDependencies point to non-existent project(s):
  bot
    bot-transcriber-script
Error: Configuration Error
The following implicitDependencies point to non-existent project(s):
  bot
    bot-transcriber-script
    at assertWorkspaceValidity (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/utils/assert-workspace-validity.js:70:11)
    at buildProjectGraphUsingProjectFileMap (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/project-graph/build-project-graph.js:48:61)
    at createAndSerializeProjectGraph (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/project-graph-incremental-recomputation.js:175:124)
    at processFilesAndCreateAndSerializeProjectGraph (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/project-graph-incremental-recomputation.js:142:16)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async getCachedSerializedProjectGraphPromise (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/project-graph-incremental-recomputation.js:43:16)
    at async handleRequestProjectGraph (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/handle-request-project-graph.js:12:24)
    at async handleResult (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/server.js:110:16)
    at async handleMessage (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/server.js:81:9)
    at async /Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/server.js:45:9
[NX Daemon Server] - 2025-05-24T17:03:06.588Z - Done responding to the client null
[NX Daemon Server] - 2025-05-24T17:03:06.731Z - Started listening on: /var/folders/qx/bdfv_k393430l_z3_sv58f7c0000gn/T/67119523c45dc649fc57/d.sock
[NX Daemon Server] - 2025-05-24T17:03:06.732Z - [WATCHER]: Subscribed to changes within: /Users/<USER>/Documents/kurt/google-meet-chrome-plugin (native)
[NX Daemon Server] - 2025-05-24T17:03:06.740Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-05-24T17:03:06.741Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-05-24T17:03:06.742Z - Closed a connection. Number of open connections: 1
[NX Daemon Server] - 2025-05-24T17:03:06.742Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-05-24T17:03:06.742Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-05-24T17:03:06.742Z - [REQUEST]: 
[NX Daemon Server] - 2025-05-24T17:03:06.742Z - [REQUEST]: 
[NX Daemon Server] - 2025-05-24T17:03:06.764Z - Error detected when creating a project graph: Configuration Error
The following implicitDependencies point to non-existent project(s):
  bot
    bot-transcriber-script
[NX Daemon Server] - 2025-05-24T17:03:06.764Z - [REQUEST]: Responding to the client with an error. Error when preparing serialized project graph. Configuration Error
The following implicitDependencies point to non-existent project(s):
  bot
    bot-transcriber-script
Error: Configuration Error
The following implicitDependencies point to non-existent project(s):
  bot
    bot-transcriber-script
    at assertWorkspaceValidity (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/utils/assert-workspace-validity.js:70:11)
    at buildProjectGraphUsingProjectFileMap (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/project-graph/build-project-graph.js:48:61)
    at createAndSerializeProjectGraph (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/project-graph-incremental-recomputation.js:175:124)
    at processFilesAndCreateAndSerializeProjectGraph (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/project-graph-incremental-recomputation.js:142:16)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async getCachedSerializedProjectGraphPromise (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/project-graph-incremental-recomputation.js:43:16)
    at async handleRequestProjectGraph (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/handle-request-project-graph.js:12:24)
    at async handleResult (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/server.js:110:16)
    at async handleMessage (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/server.js:81:9)
    at async /Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/server.js:45:9
[NX Daemon Server] - 2025-05-24T17:03:06.765Z - Done responding to the client null
[NX Daemon Server] - 2025-05-24T17:03:11.179Z - Started listening on: /var/folders/qx/bdfv_k393430l_z3_sv58f7c0000gn/T/67119523c45dc649fc57/d.sock
[NX Daemon Server] - 2025-05-24T17:03:11.181Z - [WATCHER]: Subscribed to changes within: /Users/<USER>/Documents/kurt/google-meet-chrome-plugin (native)
[NX Daemon Server] - 2025-05-24T17:03:11.184Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-05-24T17:03:11.185Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-05-24T17:03:11.190Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-05-24T17:03:11.191Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-05-24T17:03:11.191Z - Closed a connection. Number of open connections: 1
[NX Daemon Server] - 2025-05-24T17:03:11.191Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-05-24T17:03:11.192Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-05-24T17:03:11.192Z - [REQUEST]: 
[NX Daemon Server] - 2025-05-24T17:03:11.192Z - [REQUEST]: 
[NX Daemon Server] - 2025-05-24T17:03:11.218Z - Error detected when creating a project graph: Configuration Error
The following implicitDependencies point to non-existent project(s):
  bot
    bot-transcriber-script
[NX Daemon Server] - 2025-05-24T17:03:11.218Z - [REQUEST]: Responding to the client with an error. Error when preparing serialized project graph. Configuration Error
The following implicitDependencies point to non-existent project(s):
  bot
    bot-transcriber-script
Error: Configuration Error
The following implicitDependencies point to non-existent project(s):
  bot
    bot-transcriber-script
    at assertWorkspaceValidity (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/utils/assert-workspace-validity.js:70:11)
    at buildProjectGraphUsingProjectFileMap (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/project-graph/build-project-graph.js:48:61)
    at createAndSerializeProjectGraph (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/project-graph-incremental-recomputation.js:175:124)
    at processFilesAndCreateAndSerializeProjectGraph (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/project-graph-incremental-recomputation.js:142:16)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async getCachedSerializedProjectGraphPromise (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/project-graph-incremental-recomputation.js:43:16)
    at async handleRequestProjectGraph (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/handle-request-project-graph.js:12:24)
    at async handleResult (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/server.js:110:16)
    at async handleMessage (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/server.js:81:9)
    at async /Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/server.js:45:9
[NX Daemon Server] - 2025-05-24T17:03:11.218Z - Done responding to the client null
[NX Daemon Server] - 2025-05-24T17:03:11.351Z - Started listening on: /var/folders/qx/bdfv_k393430l_z3_sv58f7c0000gn/T/67119523c45dc649fc57/d.sock
[NX Daemon Server] - 2025-05-24T17:03:11.352Z - [WATCHER]: Subscribed to changes within: /Users/<USER>/Documents/kurt/google-meet-chrome-plugin (native)
[NX Daemon Server] - 2025-05-24T17:03:11.359Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-05-24T17:03:11.359Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-05-24T17:03:11.360Z - Closed a connection. Number of open connections: 1
[NX Daemon Server] - 2025-05-24T17:03:11.360Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-05-24T17:03:11.361Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-05-24T17:03:11.361Z - [REQUEST]: 
[NX Daemon Server] - 2025-05-24T17:03:11.361Z - [REQUEST]: 
[NX Daemon Server] - 2025-05-24T17:03:11.383Z - Error detected when creating a project graph: Configuration Error
The following implicitDependencies point to non-existent project(s):
  bot
    bot-transcriber-script
[NX Daemon Server] - 2025-05-24T17:03:11.383Z - [REQUEST]: Responding to the client with an error. Error when preparing serialized project graph. Configuration Error
The following implicitDependencies point to non-existent project(s):
  bot
    bot-transcriber-script
Error: Configuration Error
The following implicitDependencies point to non-existent project(s):
  bot
    bot-transcriber-script
    at assertWorkspaceValidity (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/utils/assert-workspace-validity.js:70:11)
    at buildProjectGraphUsingProjectFileMap (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/project-graph/build-project-graph.js:48:61)
    at createAndSerializeProjectGraph (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/project-graph-incremental-recomputation.js:175:124)
    at processFilesAndCreateAndSerializeProjectGraph (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/project-graph-incremental-recomputation.js:142:16)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async getCachedSerializedProjectGraphPromise (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/project-graph-incremental-recomputation.js:43:16)
    at async handleRequestProjectGraph (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/handle-request-project-graph.js:12:24)
    at async handleResult (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/server.js:110:16)
    at async handleMessage (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/server.js:81:9)
    at async /Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/server.js:45:9
[NX Daemon Server] - 2025-05-24T17:03:11.384Z - Done responding to the client null
[NX Daemon Server] - 2025-05-24T17:03:21.035Z - Started listening on: /var/folders/qx/bdfv_k393430l_z3_sv58f7c0000gn/T/67119523c45dc649fc57/d.sock
[NX Daemon Server] - 2025-05-24T17:03:21.038Z - [WATCHER]: Subscribed to changes within: /Users/<USER>/Documents/kurt/google-meet-chrome-plugin (native)
[NX Daemon Server] - 2025-05-24T17:03:21.043Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-05-24T17:03:21.044Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-05-24T17:03:21.052Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-05-24T17:03:21.052Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-05-24T17:03:21.052Z - Closed a connection. Number of open connections: 1
[NX Daemon Server] - 2025-05-24T17:03:21.052Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-05-24T17:03:21.053Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-05-24T17:03:21.053Z - [REQUEST]: 
[NX Daemon Server] - 2025-05-24T17:03:21.053Z - [REQUEST]: 
[NX Daemon Server] - 2025-05-24T17:03:21.080Z - Error detected when creating a project graph: Configuration Error
The following implicitDependencies point to non-existent project(s):
  bot
    bot-transcriber-script
[NX Daemon Server] - 2025-05-24T17:03:21.080Z - [REQUEST]: Responding to the client with an error. Error when preparing serialized project graph. Configuration Error
The following implicitDependencies point to non-existent project(s):
  bot
    bot-transcriber-script
Error: Configuration Error
The following implicitDependencies point to non-existent project(s):
  bot
    bot-transcriber-script
    at assertWorkspaceValidity (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/utils/assert-workspace-validity.js:70:11)
    at buildProjectGraphUsingProjectFileMap (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/project-graph/build-project-graph.js:48:61)
    at createAndSerializeProjectGraph (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/project-graph-incremental-recomputation.js:175:124)
    at processFilesAndCreateAndSerializeProjectGraph (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/project-graph-incremental-recomputation.js:142:16)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async getCachedSerializedProjectGraphPromise (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/project-graph-incremental-recomputation.js:43:16)
    at async handleRequestProjectGraph (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/handle-request-project-graph.js:12:24)
    at async handleResult (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/server.js:110:16)
    at async handleMessage (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/server.js:81:9)
    at async /Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/server.js:45:9
[NX Daemon Server] - 2025-05-24T17:03:21.081Z - Done responding to the client null
[NX Daemon Server] - 2025-05-24T17:03:21.241Z - Started listening on: /var/folders/qx/bdfv_k393430l_z3_sv58f7c0000gn/T/67119523c45dc649fc57/d.sock
[NX Daemon Server] - 2025-05-24T17:03:21.242Z - [WATCHER]: Subscribed to changes within: /Users/<USER>/Documents/kurt/google-meet-chrome-plugin (native)
[NX Daemon Server] - 2025-05-24T17:03:21.247Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-05-24T17:03:21.247Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-05-24T17:03:21.248Z - Closed a connection. Number of open connections: 1
[NX Daemon Server] - 2025-05-24T17:03:21.250Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-05-24T17:03:21.250Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-05-24T17:03:21.250Z - [REQUEST]: 
[NX Daemon Server] - 2025-05-24T17:03:21.250Z - [REQUEST]: 
[NX Daemon Server] - 2025-05-24T17:03:21.275Z - Error detected when creating a project graph: Configuration Error
The following implicitDependencies point to non-existent project(s):
  bot
    bot-transcriber-script
[NX Daemon Server] - 2025-05-24T17:03:21.275Z - [REQUEST]: Responding to the client with an error. Error when preparing serialized project graph. Configuration Error
The following implicitDependencies point to non-existent project(s):
  bot
    bot-transcriber-script
Error: Configuration Error
The following implicitDependencies point to non-existent project(s):
  bot
    bot-transcriber-script
    at assertWorkspaceValidity (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/utils/assert-workspace-validity.js:70:11)
    at buildProjectGraphUsingProjectFileMap (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/project-graph/build-project-graph.js:48:61)
    at createAndSerializeProjectGraph (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/project-graph-incremental-recomputation.js:175:124)
    at processFilesAndCreateAndSerializeProjectGraph (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/project-graph-incremental-recomputation.js:142:16)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async getCachedSerializedProjectGraphPromise (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/project-graph-incremental-recomputation.js:43:16)
    at async handleRequestProjectGraph (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/handle-request-project-graph.js:12:24)
    at async handleResult (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/server.js:110:16)
    at async handleMessage (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/server.js:81:9)
    at async /Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/server.js:45:9
[NX Daemon Server] - 2025-05-24T17:03:21.276Z - Done responding to the client null
[NX Daemon Server] - 2025-05-24T17:52:15.855Z - Started listening on: /var/folders/qx/bdfv_k393430l_z3_sv58f7c0000gn/T/67119523c45dc649fc57/d.sock
[NX Daemon Server] - 2025-05-24T17:52:15.857Z - [WATCHER]: Subscribed to changes within: /Users/<USER>/Documents/kurt/google-meet-chrome-plugin (native)
[NX Daemon Server] - 2025-05-24T17:52:15.863Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-05-24T17:52:15.864Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-05-24T17:52:15.869Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-05-24T17:52:15.869Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-05-24T17:52:15.869Z - Closed a connection. Number of open connections: 1
[NX Daemon Server] - 2025-05-24T17:52:15.870Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-05-24T17:52:15.870Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-05-24T17:52:15.870Z - [REQUEST]: 
[NX Daemon Server] - 2025-05-24T17:52:15.870Z - [REQUEST]: 
[NX Daemon Server] - 2025-05-24T17:52:15.905Z - Error detected when creating a project graph: Configuration Error
The following implicitDependencies point to non-existent project(s):
  bot
    bot-transcriber-script
[NX Daemon Server] - 2025-05-24T17:52:15.905Z - [REQUEST]: Responding to the client with an error. Error when preparing serialized project graph. Configuration Error
The following implicitDependencies point to non-existent project(s):
  bot
    bot-transcriber-script
Error: Configuration Error
The following implicitDependencies point to non-existent project(s):
  bot
    bot-transcriber-script
    at assertWorkspaceValidity (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/utils/assert-workspace-validity.js:70:11)
    at buildProjectGraphUsingProjectFileMap (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/project-graph/build-project-graph.js:48:61)
    at createAndSerializeProjectGraph (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/project-graph-incremental-recomputation.js:175:124)
    at processFilesAndCreateAndSerializeProjectGraph (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/project-graph-incremental-recomputation.js:142:16)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async getCachedSerializedProjectGraphPromise (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/project-graph-incremental-recomputation.js:43:16)
    at async handleRequestProjectGraph (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/handle-request-project-graph.js:12:24)
    at async handleResult (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/server.js:110:16)
    at async handleMessage (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/server.js:81:9)
    at async /Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/server.js:45:9
[NX Daemon Server] - 2025-05-24T17:52:15.906Z - Done responding to the client null
[NX Daemon Server] - 2025-05-24T17:52:16.057Z - Started listening on: /var/folders/qx/bdfv_k393430l_z3_sv58f7c0000gn/T/67119523c45dc649fc57/d.sock
[NX Daemon Server] - 2025-05-24T17:52:16.059Z - [WATCHER]: Subscribed to changes within: /Users/<USER>/Documents/kurt/google-meet-chrome-plugin (native)
[NX Daemon Server] - 2025-05-24T17:52:16.064Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-05-24T17:52:16.064Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-05-24T17:52:16.065Z - Closed a connection. Number of open connections: 1
[NX Daemon Server] - 2025-05-24T17:52:16.066Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-05-24T17:52:16.066Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-05-24T17:52:16.066Z - [REQUEST]: 
[NX Daemon Server] - 2025-05-24T17:52:16.066Z - [REQUEST]: 
[NX Daemon Server] - 2025-05-24T17:52:16.096Z - Error detected when creating a project graph: Configuration Error
The following implicitDependencies point to non-existent project(s):
  bot
    bot-transcriber-script
[NX Daemon Server] - 2025-05-24T17:52:16.096Z - [REQUEST]: Responding to the client with an error. Error when preparing serialized project graph. Configuration Error
The following implicitDependencies point to non-existent project(s):
  bot
    bot-transcriber-script
Error: Configuration Error
The following implicitDependencies point to non-existent project(s):
  bot
    bot-transcriber-script
    at assertWorkspaceValidity (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/utils/assert-workspace-validity.js:70:11)
    at buildProjectGraphUsingProjectFileMap (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/project-graph/build-project-graph.js:48:61)
    at createAndSerializeProjectGraph (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/project-graph-incremental-recomputation.js:175:124)
    at processFilesAndCreateAndSerializeProjectGraph (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/project-graph-incremental-recomputation.js:142:16)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async getCachedSerializedProjectGraphPromise (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/project-graph-incremental-recomputation.js:43:16)
    at async handleRequestProjectGraph (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/handle-request-project-graph.js:12:24)
    at async handleResult (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/server.js:110:16)
    at async handleMessage (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/server.js:81:9)
    at async /Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/server.js:45:9
[NX Daemon Server] - 2025-05-24T17:52:16.097Z - Done responding to the client null
[NX Daemon Server] - 2025-05-24T17:52:17.429Z - Started listening on: /var/folders/qx/bdfv_k393430l_z3_sv58f7c0000gn/T/67119523c45dc649fc57/d.sock
[NX Daemon Server] - 2025-05-24T17:52:17.431Z - [WATCHER]: Subscribed to changes within: /Users/<USER>/Documents/kurt/google-meet-chrome-plugin (native)
[NX Daemon Server] - 2025-05-24T17:52:17.437Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-05-24T17:52:17.438Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-05-24T17:52:17.443Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-05-24T17:52:17.443Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-05-24T17:52:17.443Z - Closed a connection. Number of open connections: 1
[NX Daemon Server] - 2025-05-24T17:52:17.444Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-05-24T17:52:17.444Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-05-24T17:52:17.444Z - [REQUEST]: 
[NX Daemon Server] - 2025-05-24T17:52:17.445Z - [REQUEST]: 
[NX Daemon Server] - 2025-05-24T17:52:17.479Z - Error detected when creating a project graph: Configuration Error
The following implicitDependencies point to non-existent project(s):
  bot
    bot-transcriber-script
[NX Daemon Server] - 2025-05-24T17:52:17.479Z - [REQUEST]: Responding to the client with an error. Error when preparing serialized project graph. Configuration Error
The following implicitDependencies point to non-existent project(s):
  bot
    bot-transcriber-script
Error: Configuration Error
The following implicitDependencies point to non-existent project(s):
  bot
    bot-transcriber-script
    at assertWorkspaceValidity (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/utils/assert-workspace-validity.js:70:11)
    at buildProjectGraphUsingProjectFileMap (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/project-graph/build-project-graph.js:48:61)
    at createAndSerializeProjectGraph (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/project-graph-incremental-recomputation.js:175:124)
    at processFilesAndCreateAndSerializeProjectGraph (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/project-graph-incremental-recomputation.js:142:16)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async getCachedSerializedProjectGraphPromise (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/project-graph-incremental-recomputation.js:43:16)
    at async handleRequestProjectGraph (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/handle-request-project-graph.js:12:24)
    at async handleResult (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/server.js:110:16)
    at async handleMessage (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/server.js:81:9)
    at async /Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/server.js:45:9
[NX Daemon Server] - 2025-05-24T17:52:17.480Z - Done responding to the client null
[NX Daemon Server] - 2025-05-24T17:52:17.595Z - Started listening on: /var/folders/qx/bdfv_k393430l_z3_sv58f7c0000gn/T/67119523c45dc649fc57/d.sock
[NX Daemon Server] - 2025-05-24T17:52:17.596Z - [WATCHER]: Subscribed to changes within: /Users/<USER>/Documents/kurt/google-meet-chrome-plugin (native)
[NX Daemon Server] - 2025-05-24T17:52:17.602Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-05-24T17:52:17.603Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-05-24T17:52:17.603Z - Closed a connection. Number of open connections: 1
[NX Daemon Server] - 2025-05-24T17:52:17.604Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-05-24T17:52:17.605Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-05-24T17:52:17.605Z - [REQUEST]: 
[NX Daemon Server] - 2025-05-24T17:52:17.605Z - [REQUEST]: 
[NX Daemon Server] - 2025-05-24T17:52:17.634Z - Error detected when creating a project graph: Configuration Error
The following implicitDependencies point to non-existent project(s):
  bot
    bot-transcriber-script
[NX Daemon Server] - 2025-05-24T17:52:17.634Z - [REQUEST]: Responding to the client with an error. Error when preparing serialized project graph. Configuration Error
The following implicitDependencies point to non-existent project(s):
  bot
    bot-transcriber-script
Error: Configuration Error
The following implicitDependencies point to non-existent project(s):
  bot
    bot-transcriber-script
    at assertWorkspaceValidity (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/utils/assert-workspace-validity.js:70:11)
    at buildProjectGraphUsingProjectFileMap (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/project-graph/build-project-graph.js:48:61)
    at createAndSerializeProjectGraph (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/project-graph-incremental-recomputation.js:175:124)
    at processFilesAndCreateAndSerializeProjectGraph (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/project-graph-incremental-recomputation.js:142:16)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async getCachedSerializedProjectGraphPromise (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/project-graph-incremental-recomputation.js:43:16)
    at async handleRequestProjectGraph (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/handle-request-project-graph.js:12:24)
    at async handleResult (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/server.js:110:16)
    at async handleMessage (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/server.js:81:9)
    at async /Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/server.js:45:9
[NX Daemon Server] - 2025-05-24T17:52:17.635Z - Done responding to the client null
[NX Daemon Server] - 2025-05-24T17:52:21.964Z - Started listening on: /var/folders/qx/bdfv_k393430l_z3_sv58f7c0000gn/T/67119523c45dc649fc57/d.sock
[NX Daemon Server] - 2025-05-24T17:52:21.965Z - [WATCHER]: Subscribed to changes within: /Users/<USER>/Documents/kurt/google-meet-chrome-plugin (native)
[NX Daemon Server] - 2025-05-24T17:52:21.974Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-05-24T17:52:21.975Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-05-24T17:52:21.980Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-05-24T17:52:21.980Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-05-24T17:52:21.981Z - Closed a connection. Number of open connections: 1
[NX Daemon Server] - 2025-05-24T17:52:21.981Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-05-24T17:52:21.982Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-05-24T17:52:21.982Z - [REQUEST]: 
[NX Daemon Server] - 2025-05-24T17:52:21.982Z - [REQUEST]: 
[NX Daemon Server] - 2025-05-24T17:52:22.019Z - Error detected when creating a project graph: Configuration Error
The following implicitDependencies point to non-existent project(s):
  bot
    bot-transcriber-script
[NX Daemon Server] - 2025-05-24T17:52:22.019Z - [REQUEST]: Responding to the client with an error. Error when preparing serialized project graph. Configuration Error
The following implicitDependencies point to non-existent project(s):
  bot
    bot-transcriber-script
Error: Configuration Error
The following implicitDependencies point to non-existent project(s):
  bot
    bot-transcriber-script
    at assertWorkspaceValidity (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/utils/assert-workspace-validity.js:70:11)
    at buildProjectGraphUsingProjectFileMap (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/project-graph/build-project-graph.js:48:61)
    at createAndSerializeProjectGraph (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/project-graph-incremental-recomputation.js:175:124)
    at processFilesAndCreateAndSerializeProjectGraph (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/project-graph-incremental-recomputation.js:142:16)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async getCachedSerializedProjectGraphPromise (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/project-graph-incremental-recomputation.js:43:16)
    at async handleRequestProjectGraph (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/handle-request-project-graph.js:12:24)
    at async handleResult (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/server.js:110:16)
    at async handleMessage (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/server.js:81:9)
    at async /Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/server.js:45:9
[NX Daemon Server] - 2025-05-24T17:52:22.020Z - Done responding to the client null
[NX Daemon Server] - 2025-05-24T17:52:22.137Z - Started listening on: /var/folders/qx/bdfv_k393430l_z3_sv58f7c0000gn/T/67119523c45dc649fc57/d.sock
[NX Daemon Server] - 2025-05-24T17:52:22.138Z - [WATCHER]: Subscribed to changes within: /Users/<USER>/Documents/kurt/google-meet-chrome-plugin (native)
[NX Daemon Server] - 2025-05-24T17:52:22.143Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-05-24T17:52:22.143Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-05-24T17:52:22.144Z - Closed a connection. Number of open connections: 1
[NX Daemon Server] - 2025-05-24T17:52:22.145Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-05-24T17:52:22.145Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-05-24T17:52:22.145Z - [REQUEST]: 
[NX Daemon Server] - 2025-05-24T17:52:22.145Z - [REQUEST]: 
[NX Daemon Server] - 2025-05-24T17:52:22.174Z - Error detected when creating a project graph: Configuration Error
The following implicitDependencies point to non-existent project(s):
  bot
    bot-transcriber-script
[NX Daemon Server] - 2025-05-24T17:52:22.174Z - [REQUEST]: Responding to the client with an error. Error when preparing serialized project graph. Configuration Error
The following implicitDependencies point to non-existent project(s):
  bot
    bot-transcriber-script
Error: Configuration Error
The following implicitDependencies point to non-existent project(s):
  bot
    bot-transcriber-script
    at assertWorkspaceValidity (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/utils/assert-workspace-validity.js:70:11)
    at buildProjectGraphUsingProjectFileMap (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/project-graph/build-project-graph.js:48:61)
    at createAndSerializeProjectGraph (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/project-graph-incremental-recomputation.js:175:124)
    at processFilesAndCreateAndSerializeProjectGraph (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/project-graph-incremental-recomputation.js:142:16)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async getCachedSerializedProjectGraphPromise (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/project-graph-incremental-recomputation.js:43:16)
    at async handleRequestProjectGraph (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/handle-request-project-graph.js:12:24)
    at async handleResult (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/server.js:110:16)
    at async handleMessage (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/server.js:81:9)
    at async /Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/server.js:45:9
[NX Daemon Server] - 2025-05-24T17:52:22.175Z - Done responding to the client null
[NX Daemon Server] - 2025-05-24T17:52:31.517Z - Started listening on: /var/folders/qx/bdfv_k393430l_z3_sv58f7c0000gn/T/67119523c45dc649fc57/d.sock
[NX Daemon Server] - 2025-05-24T17:52:31.519Z - [WATCHER]: Subscribed to changes within: /Users/<USER>/Documents/kurt/google-meet-chrome-plugin (native)
[NX Daemon Server] - 2025-05-24T17:52:31.523Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-05-24T17:52:31.525Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-05-24T17:52:31.529Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-05-24T17:52:31.530Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-05-24T17:52:31.530Z - Closed a connection. Number of open connections: 1
[NX Daemon Server] - 2025-05-24T17:52:31.530Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-05-24T17:52:31.531Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-05-24T17:52:31.531Z - [REQUEST]: 
[NX Daemon Server] - 2025-05-24T17:52:31.531Z - [REQUEST]: 
[NX Daemon Server] - 2025-05-24T17:52:31.564Z - Error detected when creating a project graph: Configuration Error
The following implicitDependencies point to non-existent project(s):
  bot
    bot-transcriber-script
[NX Daemon Server] - 2025-05-24T17:52:31.564Z - [REQUEST]: Responding to the client with an error. Error when preparing serialized project graph. Configuration Error
The following implicitDependencies point to non-existent project(s):
  bot
    bot-transcriber-script
Error: Configuration Error
The following implicitDependencies point to non-existent project(s):
  bot
    bot-transcriber-script
    at assertWorkspaceValidity (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/utils/assert-workspace-validity.js:70:11)
    at buildProjectGraphUsingProjectFileMap (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/project-graph/build-project-graph.js:48:61)
    at createAndSerializeProjectGraph (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/project-graph-incremental-recomputation.js:175:124)
    at processFilesAndCreateAndSerializeProjectGraph (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/project-graph-incremental-recomputation.js:142:16)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async getCachedSerializedProjectGraphPromise (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/project-graph-incremental-recomputation.js:43:16)
    at async handleRequestProjectGraph (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/handle-request-project-graph.js:12:24)
    at async handleResult (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/server.js:110:16)
    at async handleMessage (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/server.js:81:9)
    at async /Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/server.js:45:9
[NX Daemon Server] - 2025-05-24T17:52:31.565Z - Done responding to the client null
[NX Daemon Server] - 2025-05-24T17:52:31.674Z - Started listening on: /var/folders/qx/bdfv_k393430l_z3_sv58f7c0000gn/T/67119523c45dc649fc57/d.sock
[NX Daemon Server] - 2025-05-24T17:52:31.675Z - [WATCHER]: Subscribed to changes within: /Users/<USER>/Documents/kurt/google-meet-chrome-plugin (native)
[NX Daemon Server] - 2025-05-24T17:52:31.677Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-05-24T17:52:31.677Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-05-24T17:52:31.678Z - Closed a connection. Number of open connections: 1
[NX Daemon Server] - 2025-05-24T17:52:31.678Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-05-24T17:52:31.678Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-05-24T17:52:31.678Z - [REQUEST]: 
[NX Daemon Server] - 2025-05-24T17:52:31.678Z - [REQUEST]: 
[NX Daemon Server] - 2025-05-24T17:52:31.706Z - Error detected when creating a project graph: Configuration Error
The following implicitDependencies point to non-existent project(s):
  bot
    bot-transcriber-script
[NX Daemon Server] - 2025-05-24T17:52:31.706Z - [REQUEST]: Responding to the client with an error. Error when preparing serialized project graph. Configuration Error
The following implicitDependencies point to non-existent project(s):
  bot
    bot-transcriber-script
Error: Configuration Error
The following implicitDependencies point to non-existent project(s):
  bot
    bot-transcriber-script
    at assertWorkspaceValidity (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/utils/assert-workspace-validity.js:70:11)
    at buildProjectGraphUsingProjectFileMap (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/project-graph/build-project-graph.js:48:61)
    at createAndSerializeProjectGraph (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/project-graph-incremental-recomputation.js:175:124)
    at processFilesAndCreateAndSerializeProjectGraph (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/project-graph-incremental-recomputation.js:142:16)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async getCachedSerializedProjectGraphPromise (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/project-graph-incremental-recomputation.js:43:16)
    at async handleRequestProjectGraph (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/handle-request-project-graph.js:12:24)
    at async handleResult (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/server.js:110:16)
    at async handleMessage (/Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/server.js:81:9)
    at async /Users/<USER>/Documents/kurt/google-meet-chrome-plugin/node_modules/nx/src/daemon/server/server.js:45:9
[NX Daemon Server] - 2025-05-24T17:52:31.707Z - Done responding to the client null
